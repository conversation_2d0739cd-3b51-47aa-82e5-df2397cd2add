import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';

class SimpleStorage {
  static const String _fileName = 'app_data.json';
  static Map<String, dynamic> _cache = {};
  static bool _isInitialized = false;

  // Initialize storage
  static Future<void> _initialize() async {
    if (_isInitialized) return;
    
    try {
      final file = await _getFile();
      if (await file.exists()) {
        final contents = await file.readAsString();
        _cache = json.decode(contents) as Map<String, dynamic>;
      }
      _isInitialized = true;
    } catch (e) {
      print('Error initializing simple storage: $e');
      _cache = {};
      _isInitialized = true;
    }
  }

  // Get file reference
  static Future<File> _getFile() async {
    final directory = await getApplicationDocumentsDirectory();
    return File('${directory.path}/$_fileName');
  }

  // Save data to file
  static Future<void> _saveToFile() async {
    try {
      final file = await _getFile();
      await file.writeAsString(json.encode(_cache));
    } catch (e) {
      print('Error saving to file: $e');
    }
  }

  // Get string value
  static Future<String?> getString(String key) async {
    await _initialize();
    return _cache[key] as String?;
  }

  // Set string value
  static Future<void> setString(String key, String value) async {
    await _initialize();
    _cache[key] = value;
    await _saveToFile();
  }

  // Get string list
  static Future<List<String>?> getStringList(String key) async {
    await _initialize();
    final value = _cache[key];
    if (value is List) {
      return value.cast<String>();
    }
    return null;
  }

  // Set string list
  static Future<void> setStringList(String key, List<String> value) async {
    await _initialize();
    _cache[key] = value;
    await _saveToFile();
  }

  // Remove key
  static Future<void> remove(String key) async {
    await _initialize();
    _cache.remove(key);
    await _saveToFile();
  }

  // Clear all data
  static Future<void> clear() async {
    await _initialize();
    _cache.clear();
    await _saveToFile();
  }

  // Check if key exists
  static Future<bool> containsKey(String key) async {
    await _initialize();
    return _cache.containsKey(key);
  }
}
