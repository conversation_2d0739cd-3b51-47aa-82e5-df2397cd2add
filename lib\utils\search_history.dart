import 'simple_storage.dart';

class SearchHistory {
  static const String _recentSearchesKey = 'recent_searches';
  static const int _maxRecentSearches = 10;

  // In-memory fallback storage
  static List<String> _fallbackSearches = [];
  static bool _useInMemoryFallback = false;

  // Get recent searches
  static Future<List<String>> getRecentSearches() async {
    if (_useInMemoryFallback) {
      return List.from(_fallbackSearches);
    }

    try {
      final searches = await SimpleStorage.getStringList(_recentSearchesKey) ?? [];
      return searches;
    } catch (e) {
      print('Error loading from simple storage, using in-memory fallback: $e');
      _useInMemoryFallback = true;
      return List.from(_fallbackSearches);
    }
  }

  // Add search to recent searches
  static Future<void> addToRecentSearches(String query) async {
    if (query.trim().isEmpty) return;

    if (_useInMemoryFallback) {
      _fallbackSearches.remove(query);
      _fallbackSearches.insert(0, query);
      if (_fallbackSearches.length > _maxRecentSearches) {
        _fallbackSearches = _fallbackSearches.take(_maxRecentSearches).toList();
      }
      return;
    }

    try {
      List<String> searches = await SimpleStorage.getStringList(_recentSearchesKey) ?? [];
      searches.remove(query);
      searches.insert(0, query);
      if (searches.length > _maxRecentSearches) {
        searches = searches.take(_maxRecentSearches).toList();
      }
      await SimpleStorage.setStringList(_recentSearchesKey, searches);
    } catch (e) {
      print('Simple storage failed, using in-memory fallback: $e');
      _useInMemoryFallback = true;
      _fallbackSearches.remove(query);
      _fallbackSearches.insert(0, query);
      if (_fallbackSearches.length > _maxRecentSearches) {
        _fallbackSearches = _fallbackSearches.take(_maxRecentSearches).toList();
      }
    }
  }

  // Remove specific search from recent searches
  static Future<void> removeFromRecentSearches(String query) async {
    if (_useInMemoryFallback) {
      _fallbackSearches.remove(query);
      return;
    }

    try {
      List<String> searches = await SimpleStorage.getStringList(_recentSearchesKey) ?? [];
      searches.remove(query);
      await SimpleStorage.setStringList(_recentSearchesKey, searches);
    } catch (e) {
      print('Simple storage failed, using in-memory fallback: $e');
      _useInMemoryFallback = true;
      _fallbackSearches.remove(query);
    }
  }

  // Clear all recent searches
  static Future<void> clearRecentSearches() async {
    if (_useInMemoryFallback) {
      _fallbackSearches.clear();
      return;
    }

    try {
      await SimpleStorage.remove(_recentSearchesKey);
    } catch (e) {
      print('Simple storage failed, using in-memory fallback: $e');
      _useInMemoryFallback = true;
      _fallbackSearches.clear();
    }
  }
}
